# Firebase Configuration
VITE_FIREBASE_API_KEY=AIzaSyDdX8QBWFiGNMqd_PGfxyCLZ97ygxuAEiU
VITE_FIREBASE_AUTH_DOMAIN=ubuntu-connect-b73ff.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=ubuntu-connect-b73ff
VITE_FIREBASE_STORAGE_BUCKET=ubuntu-connect-b73ff.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=603325119422
VITE_FIREBASE_APP_ID=1:603325119422:web:ab78b1813d0517ef5617ef
VITE_FIREBASE_MEASUREMENT_ID=G-38XQLDC9FG

# Google Services API Keys
VITE_GOOGLE_TRANSLATE_API_KEY=AIzaSyCdnK-bJLIZ4UOBrxvQM6eTiUdXInWbgQk
VITE_GOOGLE_MAPS_API_KEY=AIzaSyCdnK-bJLIZ4UOBrxvQM6eTiUdXInWbgQk

# Application Configuration
VITE_APP_ENV=development
VITE_APP_VERSION=1.0.0
VITE_APP_CULTURAL_MODERATION_ENABLED=true

# South African Specific Configuration
VITE_DEFAULT_COUNTRY=ZA
VITE_DEFAULT_LANGUAGE=en
VITE_SUPPORTED_LANGUAGES=en,af,zu,xh,st,tn,ts,ve,ss,nr,nd

# POPIA Compliance
VITE_POPIA_COMPLIANCE_ENABLED=true
VITE_DATA_RETENTION_DAYS=2555  # 7 years as per POPIA requirements

# Firebase Emulators (set to true only when running emulators)
VITE_USE_FIREBASE_EMULATORS=false
